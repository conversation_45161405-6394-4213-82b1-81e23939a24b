<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Favourite Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .container {
            padding: 24px 32px;
            height: 100%;
            position: relative;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            width: 157px;
            height: 25px;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .bell-icon {
            width: 21px;
            height: 21px;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background-color: #ff5757;
            border-radius: 6px;
        }

        /* Title */
        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #1f1f1f;
            margin-bottom: 28px;
        }

        /* Podcast List */
        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-bottom: 40px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            height: 96px;
        }

        .podcast-cover {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f1f1f;
            line-height: 1.2;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 1.2;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 1.2;
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .play-icon {
            width: 18px;
            height: 18px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 46px;
            left: 32px;
            right: 32px;
            height: 72px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 84px;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
        }

        .nav-icon.active {
            opacity: 1;
        }

        .nav-dot {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
        }

        /* Gradient overlay at bottom */
        .gradient-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 156px;
            background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <img src="images/ncast-icon.png" alt="NCast Icon" class="logo-icon">
                <img src="images/ncast-logo-text.png" alt="NCast" class="logo-text">
            </div>
            <div class="notification-container">
                <button class="notification-btn">
                    <img src="images/bell-icon.png" alt="Notifications" class="bell-icon">
                </button>
                <div class="notification-badge"></div>
            </div>
        </div>

        <!-- Page Title -->
        <h1 class="page-title">Favourite Podcasts</h1>

        <!-- Podcast List -->
        <div class="podcast-list">
            <div class="podcast-item">
                <img src="images/podcast-cover-1.png" alt="Sunday Summer - Ep3" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Sunday Summer - Ep3</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-1.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-2.png" alt="Musical Soul - Vol. 1" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Musical Soul - Vol. 1</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">35 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-2.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-3.png" alt="Talk Show - Ep4" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Talk Show - Ep4</div>
                    <div class="podcast-category">Business</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-3.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-4.png" alt="Musical Soul - Vol. 2" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Musical Soul - Vol. 2</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">30 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-4.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-5.png" alt="Unravelling The Mind" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Unravelling The Mind</div>
                    <div class="podcast-category">Healthy Lifestyle</div>
                    <div class="podcast-duration">10 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-5.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-6.png" alt="Talk Show - Ep8" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Talk Show - Ep8</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-6.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- Gradient Overlay -->
        <div class="gradient-overlay"></div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <img src="images/headphones-icon.png" alt="Headphones" class="nav-icon">
            <img src="images/compass-icon.png" alt="Discover" class="nav-icon">
            <img src="images/heart-icon.png" alt="Favorites" class="nav-icon active">
            <img src="images/profile-icon.png" alt="Profile" class="nav-icon">
            <div class="nav-dot"></div>
        </div>
    </div>
</body>
</html>
